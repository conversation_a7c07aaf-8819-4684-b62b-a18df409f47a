# This is a RocksDB option file.
#
# For detailed file format spec, please refer to the example file
# in examples/rocksdb_option_file_example.ini
#

[Version]
  rocksdb_version=9.9.3
  options_file_version=1.1

[DBOptions]
  max_background_flushes=-1
  compaction_readahead_size=2097152
  strict_bytes_per_sync=false
  wal_bytes_per_sync=0
  max_open_files=256
  stats_history_buffer_size=1048576
  max_total_wal_size=0
  stats_persist_period_sec=600
  stats_dump_period_sec=600
  avoid_flush_during_shutdown=false
  max_subcompactions=1
  bytes_per_sync=0
  delayed_write_rate=16777216
  max_background_compactions=-1
  max_background_jobs=2
  delete_obsolete_files_period_micros=180000000
  writable_file_max_buffer_size=1048576
  follower_catchup_retry_wait_ms=100
  file_checksum_gen_factory=nullptr
  allow_data_in_errors=false
  max_bgerror_resume_count=2147483647
  best_efforts_recovery=false
  wal_write_temperature=kUnknown
  write_identity_file=true
  write_dbid_to_manifest=true
  atomic_flush=false
  manual_wal_flush=false
  two_write_queues=false
  avoid_flush_during_recovery=false
  dump_malloc_stats=false
  info_log_level=ERROR_LEVEL
  write_thread_slow_yield_usec=3
  unordered_write=false
  allow_ingest_behind=false
  fail_if_options_file_error=true
  persist_stats_to_disk=false
  WAL_ttl_seconds=0
  bgerror_resume_retry_interval=1000000
  allow_concurrent_memtable_write=true
  paranoid_checks=true
  WAL_size_limit_MB=0
  metadata_write_temperature=kUnknown
  lowest_used_cache_tier=kNonVolatileBlockTier
  keep_log_file_num=1
  table_cache_numshardbits=6
  max_file_opening_threads=16
  random_access_max_buffer_size=1048576
  follower_refresh_catchup_period_ms=10000
  log_readahead_size=0
  enable_pipelined_write=false
  background_close_inactive_wals=false
  wal_recovery_mode=kTolerateCorruptedTailRecords
  follower_catchup_retry_count=10
  db_write_buffer_size=0
  allow_2pc=false
  skip_checking_sst_file_sizes_on_db_open=false
  skip_stats_update_on_db_open=false
  recycle_log_file_num=0
  db_host_id=__hostname__
  track_and_verify_wals_in_manifest=false
  use_fsync=false
  wal_compression=kNoCompression
  compaction_verify_record_count=true
  error_if_exists=false
  manifest_preallocation_size=4194304
  is_fd_close_on_exec=true
  enable_write_thread_adaptive_yield=true
  enable_thread_tracking=false
  avoid_unnecessary_blocking_io=false
  allow_fallocate=true
  max_log_file_size=1048576
  advise_random_on_open=true
  create_missing_column_families=true
  max_write_batch_group_size_bytes=1048576
  use_adaptive_mutex=false
  prefix_seek_opt_in_only=false
  wal_filter=nullptr
  create_if_missing=true
  enforce_single_del_contracts=true
  allow_mmap_writes=false
  verify_sst_unique_id_in_manifest=true
  log_file_time_to_roll=0
  use_direct_io_for_flush_and_compaction=false
  flush_verify_memtable_count=true
  max_manifest_file_size=1073741824
  write_thread_max_yield_usec=100
  use_direct_reads=false
  allow_mmap_reads=false
  

[CFOptions "default"]
  bottommost_file_compaction_delay=0
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_compression_type=kNoCompression
  paranoid_memory_checks=false
  blob_garbage_collection_age_cutoff=0.250000
  preclude_last_level_data_seconds=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;max_read_amp=-1;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=68719476736
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={file_temperature_age_thresholds=;allow_compaction=false;age_for_warm=0;max_table_files_size=1073741824;}
  memtable_huge_page_size=0
  max_sequential_skip_in_iterations=8
  strict_max_successive_merges=false
  max_successive_merges=0
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;zstd_max_train_bytes=0;parallel_threads=1;max_compressed_bytes_per_kb=896;checksum=false;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  min_blob_size=0
  level0_stop_writes_trigger=36
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  default_write_temperature=kUnknown
  compression=kLZ4Compression
  level0_file_num_compaction_trigger=4
  block_protection_bytes_per_key=0
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=10485760
  uncache_aggressiveness=0
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  inplace_update_num_locks=10000
  periodic_compaction_seconds=0
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=268435456
  paranoid_file_checks=false
  blob_file_size=268435456
  preserve_internal_time_seconds=0
  memtable_max_range_deletions=0
  compression_opts={use_zstd_dict_trainer=true;enabled=false;zstd_max_train_bytes=0;parallel_threads=1;max_compressed_bytes_per_kb=896;checksum=false;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  last_level_temperature=kUnknown
  table_factory=BlockBasedTable
  report_bg_io_stats=false
  persist_user_defined_timestamps=true
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  optimize_filters_for_hits=false
  default_temperature=kUnknown
  force_consistency_checks=true
  num_levels=7
  merge_operator=nullptr
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=true
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "default"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=6
  detect_filter_construct_corruption=false
  optimize_filters_for_memory=true
  decouple_partitioned_filters=false
  partition_filters=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  use_delta_encoding=true
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "pathSegments.0_map"]
  bottommost_file_compaction_delay=0
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_compression_type=kNoCompression
  paranoid_memory_checks=false
  blob_garbage_collection_age_cutoff=0.250000
  preclude_last_level_data_seconds=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;max_read_amp=-1;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=68719476736
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={file_temperature_age_thresholds=;allow_compaction=false;age_for_warm=0;max_table_files_size=1073741824;}
  memtable_huge_page_size=0
  max_sequential_skip_in_iterations=8
  strict_max_successive_merges=false
  max_successive_merges=0
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;zstd_max_train_bytes=0;parallel_threads=1;max_compressed_bytes_per_kb=896;checksum=false;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  min_blob_size=0
  level0_stop_writes_trigger=36
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  default_write_temperature=kUnknown
  compression=kLZ4Compression
  level0_file_num_compaction_trigger=4
  block_protection_bytes_per_key=0
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=10485760
  uncache_aggressiveness=0
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  inplace_update_num_locks=10000
  periodic_compaction_seconds=0
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=268435456
  paranoid_file_checks=false
  blob_file_size=268435456
  preserve_internal_time_seconds=0
  memtable_max_range_deletions=0
  compression_opts={use_zstd_dict_trainer=true;enabled=false;zstd_max_train_bytes=0;parallel_threads=1;max_compressed_bytes_per_kb=896;checksum=false;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  last_level_temperature=kUnknown
  table_factory=BlockBasedTable
  report_bg_io_stats=false
  persist_user_defined_timestamps=true
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  optimize_filters_for_hits=false
  default_temperature=kUnknown
  force_consistency_checks=true
  num_levels=7
  merge_operator=nullptr
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=true
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "pathSegments.0_map"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=6
  detect_filter_construct_corruption=false
  optimize_filters_for_memory=true
  decouple_partitioned_filters=false
  partition_filters=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  use_delta_encoding=true
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "pathSegments.1_map"]
  bottommost_file_compaction_delay=0
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_compression_type=kNoCompression
  paranoid_memory_checks=false
  blob_garbage_collection_age_cutoff=0.250000
  preclude_last_level_data_seconds=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;max_read_amp=-1;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=68719476736
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={file_temperature_age_thresholds=;allow_compaction=false;age_for_warm=0;max_table_files_size=1073741824;}
  memtable_huge_page_size=0
  max_sequential_skip_in_iterations=8
  strict_max_successive_merges=false
  max_successive_merges=0
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;zstd_max_train_bytes=0;parallel_threads=1;max_compressed_bytes_per_kb=896;checksum=false;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  min_blob_size=0
  level0_stop_writes_trigger=36
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  default_write_temperature=kUnknown
  compression=kLZ4Compression
  level0_file_num_compaction_trigger=4
  block_protection_bytes_per_key=0
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=10485760
  uncache_aggressiveness=0
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  inplace_update_num_locks=10000
  periodic_compaction_seconds=0
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=268435456
  paranoid_file_checks=false
  blob_file_size=268435456
  preserve_internal_time_seconds=0
  memtable_max_range_deletions=0
  compression_opts={use_zstd_dict_trainer=true;enabled=false;zstd_max_train_bytes=0;parallel_threads=1;max_compressed_bytes_per_kb=896;checksum=false;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  last_level_temperature=kUnknown
  table_factory=BlockBasedTable
  report_bg_io_stats=false
  persist_user_defined_timestamps=true
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  optimize_filters_for_hits=false
  default_temperature=kUnknown
  force_consistency_checks=true
  num_levels=7
  merge_operator=nullptr
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=true
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "pathSegments.1_map"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=6
  detect_filter_construct_corruption=false
  optimize_filters_for_memory=true
  decouple_partitioned_filters=false
  partition_filters=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  use_delta_encoding=true
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "pathSegments.2_map"]
  bottommost_file_compaction_delay=0
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_compression_type=kNoCompression
  paranoid_memory_checks=false
  blob_garbage_collection_age_cutoff=0.250000
  preclude_last_level_data_seconds=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;max_read_amp=-1;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=68719476736
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={file_temperature_age_thresholds=;allow_compaction=false;age_for_warm=0;max_table_files_size=1073741824;}
  memtable_huge_page_size=0
  max_sequential_skip_in_iterations=8
  strict_max_successive_merges=false
  max_successive_merges=0
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;zstd_max_train_bytes=0;parallel_threads=1;max_compressed_bytes_per_kb=896;checksum=false;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  min_blob_size=0
  level0_stop_writes_trigger=36
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  default_write_temperature=kUnknown
  compression=kLZ4Compression
  level0_file_num_compaction_trigger=4
  block_protection_bytes_per_key=0
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=10485760
  uncache_aggressiveness=0
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  inplace_update_num_locks=10000
  periodic_compaction_seconds=0
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=268435456
  paranoid_file_checks=false
  blob_file_size=268435456
  preserve_internal_time_seconds=0
  memtable_max_range_deletions=0
  compression_opts={use_zstd_dict_trainer=true;enabled=false;zstd_max_train_bytes=0;parallel_threads=1;max_compressed_bytes_per_kb=896;checksum=false;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  last_level_temperature=kUnknown
  table_factory=BlockBasedTable
  report_bg_io_stats=false
  persist_user_defined_timestamps=true
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  optimize_filters_for_hits=false
  default_temperature=kUnknown
  force_consistency_checks=true
  num_levels=7
  merge_operator=nullptr
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=true
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "pathSegments.2_map"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=6
  detect_filter_construct_corruption=false
  optimize_filters_for_memory=true
  decouple_partitioned_filters=false
  partition_filters=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  use_delta_encoding=true
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "pathSegments.3_map"]
  bottommost_file_compaction_delay=0
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_compression_type=kNoCompression
  paranoid_memory_checks=false
  blob_garbage_collection_age_cutoff=0.250000
  preclude_last_level_data_seconds=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;max_read_amp=-1;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=68719476736
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={file_temperature_age_thresholds=;allow_compaction=false;age_for_warm=0;max_table_files_size=1073741824;}
  memtable_huge_page_size=0
  max_sequential_skip_in_iterations=8
  strict_max_successive_merges=false
  max_successive_merges=0
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;zstd_max_train_bytes=0;parallel_threads=1;max_compressed_bytes_per_kb=896;checksum=false;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  min_blob_size=0
  level0_stop_writes_trigger=36
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  default_write_temperature=kUnknown
  compression=kLZ4Compression
  level0_file_num_compaction_trigger=4
  block_protection_bytes_per_key=0
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=10485760
  uncache_aggressiveness=0
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  inplace_update_num_locks=10000
  periodic_compaction_seconds=0
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=268435456
  paranoid_file_checks=false
  blob_file_size=268435456
  preserve_internal_time_seconds=0
  memtable_max_range_deletions=0
  compression_opts={use_zstd_dict_trainer=true;enabled=false;zstd_max_train_bytes=0;parallel_threads=1;max_compressed_bytes_per_kb=896;checksum=false;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  last_level_temperature=kUnknown
  table_factory=BlockBasedTable
  report_bg_io_stats=false
  persist_user_defined_timestamps=true
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  optimize_filters_for_hits=false
  default_temperature=kUnknown
  force_consistency_checks=true
  num_levels=7
  merge_operator=nullptr
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=true
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "pathSegments.3_map"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=6
  detect_filter_construct_corruption=false
  optimize_filters_for_memory=true
  decouple_partitioned_filters=false
  partition_filters=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  use_delta_encoding=true
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  
