<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>SILLYPASS - Test</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="popup.css" />
    <style>
      /* Test container to simulate extension popup */
      .test-container {
        width: 320px;
        height: 600px;
        border: 2px solid #ccc;
        margin: 20px auto;
        overflow-y: auto;
        background: white;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <div class="bg-sillypass ext" style="width: 100%; height: 100%;">
        <!-- Popup content mounts here -->
        <div id="root"></div>
      </div>
    </div>

    <!-- Popup script -->
    <script src="popup.js"></script>
  </body>
</html>