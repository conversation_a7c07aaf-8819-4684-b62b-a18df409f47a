#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Build script to prepare Chrome extension from Vite build output
console.log('🔧 Building Chrome Extension...');

const distDir = path.join(__dirname, '../dist');
const chromeExtDir = path.join(__dirname, '../chrome-ext');

// Ensure chrome-ext directory exists
if (!fs.existsSync(chromeExtDir)) {
  fs.mkdirSync(chromeExtDir, { recursive: true });
}

// Copy built assets to chrome-ext
function copyRecursiveSync(src, dest) {
  const exists = fs.existsSync(src);
  const stats = exists && fs.statSync(src);
  const isDirectory = exists && stats.isDirectory();
  
  if (isDirectory) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest);
    }
    fs.readdirSync(src).forEach(childItemName => {
      copyRecursiveSync(path.join(src, childItemName), path.join(dest, childItemName));
    });
  } else {
    fs.copyFileSync(src, dest);
  }
}

// Copy dist assets to chrome-ext/dist
const chromeDistDir = path.join(chromeExtDir, 'dist');
if (fs.existsSync(chromeDistDir)) {
  fs.rmSync(chromeDistDir, { recursive: true });
}
copyRecursiveSync(distDir, chromeDistDir);

// Update popup.html to reference correct asset paths
const popupHtmlPath = path.join(chromeExtDir, 'popup.html');
let popupHtml = fs.readFileSync(popupHtmlPath, 'utf8');

// Find the actual CSS and JS filenames from the build
const assetsDir = path.join(chromeDistDir, 'assets');
const cssFiles = fs.readdirSync(assetsDir).filter(f => f.endsWith('.css'));
const jsFiles = fs.readdirSync(assetsDir).filter(f => f.endsWith('.js'));

// Update CSS reference in popup.html if needed
if (cssFiles.length > 0) {
  popupHtml = popupHtml.replace(
    'popup.css',
    `dist/assets/${cssFiles[0]}`
  );
}

// Ensure the JS reference is correct (should already be index.js due to vite config)
console.log(`✅ Built JS bundle: ${jsFiles[0]}`);
console.log(`✅ Built CSS bundle: ${cssFiles[0]}`);

fs.writeFileSync(popupHtmlPath, popupHtml);

// Use existing icon files if they exist, otherwise create placeholders
const iconsDir = path.join(chromeExtDir, 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir);
}

// Check if proper icon files exist and copy them with correct names
[16, 32, 48, 128].forEach(size => {
  const sourceIconPath = path.join(iconsDir, `icon${size}.png`);
  const targetIconPath = path.join(iconsDir, `${size}.png`);
  
  if (fs.existsSync(sourceIconPath)) {
    // Copy the existing icon file
    fs.copyFileSync(sourceIconPath, targetIconPath);
    console.log(`✅ Using existing icon: icon${size}.png -> ${size}.png`);
  } else {
    // Create placeholder if icon doesn't exist
    const createIcon = (size) => `
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" rx="${size/8}" fill="#8b5cf6"/>
  <text x="50%" y="50%" text-anchor="middle" dy="0.35em" fill="white" font-family="Arial, sans-serif" font-weight="bold" font-size="${size/3}">S</text>
</svg>
`;
    fs.writeFileSync(targetIconPath, createIcon(size));
    console.log(`⚠️  Created placeholder icon: ${size}.png`);
  }
});

console.log('✅ Chrome Extension built successfully!');
console.log('📁 Extension files are in: chrome-ext/');
console.log('🚀 Load unpacked extension from chrome-ext/ folder in Chrome');